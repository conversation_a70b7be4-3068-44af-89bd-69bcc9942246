import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Users, Target, Award, Heart, Shield } from "lucide-react";

const About = () => {
  const features = [
    {
      icon: Target,
      title: "Our Mission",
      description: "To bridge the gap between citizens and government by providing a transparent, efficient platform for reporting and resolving civic issues."
    },
    {
      icon: Users,
      title: "Community First",
      description: "We believe in the power of community engagement and collaborative problem-solving to build better cities and towns."
    },
    {
      icon: Shield,
      title: "Transparency",
      description: "Every report is tracked with complete transparency, ensuring accountability and trust in the resolution process."
    },
    {
      icon: Award,
      title: "Excellence",
      description: "We strive for excellence in service delivery, ensuring quick response times and effective solutions."
    }
  ];

  const stats = [
    { number: "10,000+", label: "Issues Reported" },
    { number: "8,500+", label: "Issues Resolved" },
    { number: "50+", label: "Government Departments" },
    { number: "85%", label: "Resolution Rate" }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
              <MapPin className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            About CivicReport
          </h1>
          <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            Empowering citizens and government to work together for better communities through technology-driven civic engagement.
          </p>
          <Badge variant="secondary" className="text-lg px-6 py-2">
            Government of Jharkhand Initiative
          </Badge>
        </div>
      </section>

      {/* Mission & Values */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Our Values & Mission
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We are committed to creating a more responsive and accountable government through citizen engagement.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center shadow-medium hover:shadow-strong transition-shadow">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Our Impact
            </h2>
            <p className="text-lg text-muted-foreground">
              Making a real difference in communities across Jharkhand
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl font-bold text-primary mb-2">
                  {stat.number}
                </div>
                <div className="text-muted-foreground">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              How CivicReport Works
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              A simple, transparent process that connects citizens with government services
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center shadow-medium">
              <CardHeader>
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">1</span>
                </div>
                <CardTitle>Report Issue</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Citizens report civic issues with photos, location, and detailed descriptions through our easy-to-use platform.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center shadow-medium">
              <CardHeader>
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-green-600">2</span>
                </div>
                <CardTitle>Smart Routing</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Issues are automatically routed to the appropriate government department based on category and location.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center shadow-medium">
              <CardHeader>
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-purple-600">3</span>
                </div>
                <CardTitle>Track & Resolve</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Citizens receive real-time updates on their reports while government agencies work to resolve issues efficiently.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <Heart className="h-12 w-12 mx-auto mb-6 text-white/80" />
          <h3 className="text-3xl font-bold mb-4">
            Together, We Build Better Communities
          </h3>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join thousands of citizens and government officials working together to make Jharkhand a better place to live.
          </p>
        </div>
      </section>
    </div>
  );
};

export default About;

import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { supabase } from "@/integrations/supabase/client";
import { 
  ArrowLeft, 
  MapPin, 
  Calendar, 
  User, 
  FileText, 
  Camera,
  Clock,
  CheckCircle,
  AlertCircle,
  Eye,
  XCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";

interface CivicIssue {
  id: string;
  title: string;
  description: string;
  category: string;
  priority: string;
  status: string;
  location_lat: number;
  location_lng: number;
  location_address: string;
  photo_url: string;
  voice_note_url: string;
  department: string;
  resolution_notes: string;
  created_at: string;
  updated_at: string;
  resolved_at: string;
}

interface IssueUpdate {
  id: string;
  old_status: string;
  new_status: string;
  notes: string;
  created_at: string;
  user_id: string;
}

const ReportDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [issue, setIssue] = useState<CivicIssue | null>(null);
  const [updates, setUpdates] = useState<IssueUpdate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchIssueDetails();
      fetchIssueUpdates();
    }
  }, [id]);

  const fetchIssueDetails = async () => {
    try {
      const { data, error } = await supabase
        .from('civic_issues')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      setIssue(data);
    } catch (error) {
      setError("Failed to load issue details");
      toast({
        title: "Error",
        description: "Failed to load issue details",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchIssueUpdates = async () => {
    try {
      const { data, error } = await supabase
        .from('issue_updates')
        .select('*')
        .eq('issue_id', id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUpdates(data || []);
    } catch (error) {
      console.error('Error fetching updates:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "submitted":
        return <Clock className="h-5 w-5" />;
      case "in_review":
        return <Eye className="h-5 w-5" />;
      case "in_progress":
        return <AlertCircle className="h-5 w-5" />;
      case "resolved":
        return <CheckCircle className="h-5 w-5" />;
      case "closed":
        return <XCircle className="h-5 w-5" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "submitted":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "in_review":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "in_progress":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
      case "resolved":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "closed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      case "rejected":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "low":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "high":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
      case "urgent":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const formatCategory = (category: string) => {
    return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading issue details...</div>
      </div>
    );
  }

  if (error || !issue) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertDescription>
            {error || "Issue not found"}
          </AlertDescription>
        </Alert>
        <Button 
          variant="outline" 
          onClick={() => navigate('/my-reports')} 
          className="mt-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to My Reports
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={() => navigate('/my-reports')}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to My Reports
        </Button>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Issue Header */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-2xl mb-3">{issue.title}</CardTitle>
                  <div className="flex flex-wrap items-center gap-2 mb-4">
                    <Badge variant="secondary">
                      {formatCategory(issue.category)}
                    </Badge>
                    <Badge className={`${getStatusColor(issue.status)} flex items-center space-x-1`}>
                      {getStatusIcon(issue.status)}
                      <span>{issue.status.replace(/_/g, ' ')}</span>
                    </Badge>
                    <Badge className={getPriorityColor(issue.priority)}>
                      {issue.priority} priority
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-base leading-relaxed">
                {issue.description}
              </CardDescription>
            </CardContent>
          </Card>

          {/* Photo */}
          {issue.photo_url && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Camera className="h-5 w-5" />
                  <span>Photo Evidence</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <img 
                  src={issue.photo_url} 
                  alt="Issue evidence" 
                  className="w-full h-auto rounded-lg shadow-soft"
                />
              </CardContent>
            </Card>
          )}

          {/* Resolution Notes */}
          {issue.resolution_notes && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-success" />
                  <span>Resolution Notes</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{issue.resolution_notes}</p>
              </CardContent>
            </Card>
          )}

          {/* Issue Updates Timeline */}
          {updates.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Status Updates</CardTitle>
                <CardDescription>
                  Track the progress of your report
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {updates.map((update, index) => (
                    <div key={update.id} className="flex space-x-4">
                      <div className="flex flex-col items-center">
                        <div className={`p-2 rounded-full ${getStatusColor(update.new_status)}`}>
                          {getStatusIcon(update.new_status)}
                        </div>
                        {index < updates.length - 1 && (
                          <div className="w-px h-8 bg-border mt-2"></div>
                        )}
                      </div>
                      <div className="flex-1 pb-4">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-medium">
                            Status changed to {update.new_status.replace(/_/g, ' ')}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {format(new Date(update.created_at), 'MMM dd, yyyy HH:mm')}
                          </span>
                        </div>
                        {update.notes && (
                          <p className="text-sm text-muted-foreground">{update.notes}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Issue Info */}
          <Card>
            <CardHeader>
              <CardTitle>Issue Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Reported</p>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(issue.created_at), 'MMM dd, yyyy HH:mm')}
                  </p>
                </div>
              </div>

              {issue.updated_at !== issue.created_at && (
                <div className="flex items-center space-x-3">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Last Updated</p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(issue.updated_at), 'MMM dd, yyyy HH:mm')}
                    </p>
                  </div>
                </div>
              )}

              {issue.resolved_at && (
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-4 w-4 text-success" />
                  <div>
                    <p className="text-sm font-medium">Resolved</p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(issue.resolved_at), 'MMM dd, yyyy HH:mm')}
                    </p>
                  </div>
                </div>
              )}

              {issue.department && (
                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Assigned Department</p>
                    <p className="text-sm text-muted-foreground">{issue.department}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Location */}
          {issue.location_address && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5" />
                  <span>Location</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-3">
                  {issue.location_address}
                </p>
                {issue.location_lat && issue.location_lng && (
                  <div className="bg-muted rounded-lg p-4 text-center">
                    <p className="text-xs text-muted-foreground">
                      Coordinates: {issue.location_lat.toFixed(6)}, {issue.location_lng.toFixed(6)}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full" onClick={() => navigate('/my-reports')}>
                <FileText className="h-4 w-4 mr-2" />
                View All Reports
              </Button>
              <Button className="w-full bg-gradient-primary hover:opacity-90" onClick={() => navigate('/report')}>
                Report New Issue
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ReportDetails;
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Camera, Users, BarChart3, CheckCircle, AlertTriangle, Wrench, Lightbulb } from "lucide-react";
import { useNavigate } from "react-router-dom";
import heroImage from "@/assets/civic-hero.jpg";

const Index = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: Camera,
      title: "Photo Reports",
      description: "Capture issues with photos and automatic location tagging"
    },
    {
      icon: MapPin,
      title: "Smart Routing",
      description: "Issues automatically routed to the right department"
    },
    {
      icon: Users,
      title: "Community Driven",
      description: "Citizens and government working together for better cities"
    },
    {
      icon: BarChart3,
      title: "Analytics Dashboard",
      description: "Track resolution times and identify priority areas"
    }
  ];

  const issueTypes = [
    { icon: AlertTriangle, label: "Road Issues", count: 12, color: "warning" },
    { icon: Lightbulb, label: "Street Lighting", count: 8, color: "accent" },
    { icon: Wrench, label: "Infrastructure", count: 5, color: "primary" },
    { icon: CheckCircle, label: "Sanitation", count: 15, color: "success" },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-hero opacity-90"></div>
        <div className="relative container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Report. Track. Resolve.
            </h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              Help make your community better by reporting civic issues. Government agencies can track and resolve problems efficiently with real-time updates.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                variant="hero" 
                size="lg" 
                className="text-lg"
                onClick={() => navigate('/report')}
              >
                Report an Issue
              </Button>
              <Button 
                variant="outline" 
                size="lg" 
                className="text-lg bg-white/10 border-white/30 text-white hover:bg-white/20"
                onClick={() => navigate('/admin')}
              >
                Admin Dashboard
              </Button>
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-4xl">
          <img 
            src={heroImage} 
            alt="Civic reporting technology" 
            className="w-full h-auto rounded-t-2xl shadow-strong"
          />
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-foreground mb-4">
              Streamlined Civic Engagement
            </h3>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Our platform connects citizens with government agencies for faster issue resolution and better community outcomes.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center shadow-soft hover:shadow-medium transition-all duration-200 border-0 bg-gradient-card">
                <CardHeader>
                  <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <feature.icon className="h-8 w-8 text-white" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Issue Types Overview */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-foreground mb-4">
              Current Issue Categories
            </h3>
            <p className="text-xl text-muted-foreground">
              Overview of reported issues by category
            </p>
          </div>
          
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
            {issueTypes.map((type, index) => (
              <Card key={index} className="text-center shadow-soft hover:shadow-medium transition-all duration-200 cursor-pointer">
                <CardContent className="pt-6">
                  <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <type.icon className="h-6 w-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-foreground mb-2">{type.label}</h4>
                  <Badge variant="secondary" className="text-lg font-bold">
                    {type.count} Issues
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <h3 className="text-3xl font-bold mb-4">
            Ready to Make a Difference?
          </h3>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join thousands of citizens and government officials working together to build better communities.
          </p>
          <Button 
            variant="hero" 
            size="lg" 
            className="bg-white text-civic-green hover:bg-white/90"
            onClick={() => navigate('/report')}
          >
            Get Started Today
          </Button>
        </div>
      </section>

    </div>
  );
};

export default Index;
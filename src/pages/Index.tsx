import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  MapPin,
  Camera,
  Users,
  BarChart3,
  CheckCircle,
  AlertTriangle,
  Wrench,
  Lightbulb,
  Sparkles,
  Shield,
  Zap,
  Heart,
  ArrowRight,
  Star,
  TrendingUp,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import heroImage from "@/assets/civic-hero.jpg";

const Index = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: Camera,
      title: "Smart Photo Reports",
      description:
        "AI-powered issue detection with automatic location tagging and category suggestions",
      color: "text-blue-500",
      bgColor: "bg-blue-50 dark:bg-blue-950/20",
    },
    {
      icon: Zap,
      title: "Lightning Fast",
      description:
        "Real-time routing to departments with instant notifications and updates",
      color: "text-yellow-500",
      bgColor: "bg-yellow-50 dark:bg-yellow-950/20",
    },
    {
      icon: Users,
      title: "Community Driven",
      description:
        "Citizens and government collaborating for transparent, accountable governance",
      color: "text-green-500",
      bgColor: "bg-green-50 dark:bg-green-950/20",
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description:
        "Data-driven insights for better decision making and resource allocation",
      color: "text-purple-500",
      bgColor: "bg-purple-50 dark:bg-purple-950/20",
    },
  ];

  const stats = [
    { number: "10,000+", label: "Issues Resolved", icon: CheckCircle },
    { number: "50+", label: "Departments", icon: Shield },
    { number: "95%", label: "Satisfaction Rate", icon: Star },
    { number: "2.4 days", label: "Avg Resolution", icon: TrendingUp },
  ];

  const issueTypes = [
    { icon: AlertTriangle, label: "Road Issues", count: 12, color: "warning" },
    { icon: Lightbulb, label: "Street Lighting", count: 8, color: "accent" },
    { icon: Wrench, label: "Infrastructure", count: 5, color: "primary" },
    { icon: CheckCircle, label: "Sanitation", count: 15, color: "success" },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden min-h-screen flex items-center">
        <div className="absolute inset-0 bg-gradient-hero opacity-95"></div>
        <div className="absolute inset-0 opacity-30">
          <div className="w-full h-full bg-gradient-to-tr from-white/5 via-transparent to-white/5"></div>
        </div>

        <div className="relative container mx-auto px-4 text-center z-10">
          <div className="max-w-5xl mx-auto">
            {/* Floating badge */}
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-md border border-white/30 rounded-full px-6 py-2 mb-8 animate-float">
              <Sparkles className="h-4 w-4 text-yellow-300" />
              <span className="text-white/90 text-sm font-medium">
                Trusted by 50+ Government Departments
              </span>
            </div>

            <h1 className="text-5xl md:text-7xl lg:text-8xl font-black text-white mb-8 leading-tight">
              <span className="bg-gradient-to-r from-white via-blue-100 to-green-100 bg-clip-text text-transparent">
                Report.
              </span>
              <br />
              <span className="bg-gradient-to-r from-green-100 via-yellow-100 to-white bg-clip-text text-transparent">
                Track.
              </span>
              <br />
              <span className="bg-gradient-to-r from-yellow-100 via-white to-blue-100 bg-clip-text text-transparent">
                Resolve.
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-white/90 mb-12 max-w-3xl mx-auto leading-relaxed">
              Transform your community with AI-powered civic engagement.
              <span className="font-semibold text-yellow-200">
                {" "}
                Real-time reporting
              </span>
              ,
              <span className="font-semibold text-green-200">
                {" "}
                transparent tracking
              </span>
              , and
              <span className="font-semibold text-blue-200">
                {" "}
                efficient resolution
              </span>
              .
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Button
                size="lg"
                className="text-lg px-8 py-4 bg-white text-gray-900 hover:bg-white/90 shadow-glow hover:shadow-strong transition-all duration-300 group"
                onClick={() => navigate("/report")}
              >
                <Camera className="h-5 w-5 mr-2 group-hover:rotate-12 transition-transform" />
                Report an Issue
                <ArrowRight className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-4 bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-md transition-all duration-300"
                onClick={() => navigate("/admin")}
              >
                <Shield className="h-5 w-5 mr-2" />
                Admin Dashboard
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="glass-card rounded-2xl p-6 text-center animate-slide-up"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <stat.icon className="h-8 w-8 text-white/80 mx-auto mb-3" />
                  <div className="text-2xl md:text-3xl font-bold text-white mb-1">
                    {stat.number}
                  </div>
                  <div className="text-white/70 text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Floating hero image */}
        <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 w-full max-w-5xl animate-float">
          <div className="relative">
            <img
              src={heroImage}
              alt="Civic reporting technology"
              className="w-full h-auto rounded-3xl shadow-strong border-4 border-white/20"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-3xl"></div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-32 bg-gradient-to-br from-background via-muted/20 to-background relative overflow-hidden">
        <div className="absolute inset-0 opacity-50">
          <div className="w-full h-full bg-gradient-to-br from-transparent via-primary/5 to-transparent"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-primary/10 text-primary rounded-full px-4 py-2 mb-6">
              <Heart className="h-4 w-4" />
              <span className="text-sm font-medium">Built for Communities</span>
            </div>
            <h2 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
              Why Choose
              <span className="bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent">
                {" "}
                CivicReport
              </span>
              ?
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Modern technology meets civic responsibility. Our platform makes
              reporting and resolving community issues simple, transparent, and
              delightfully effective.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="group shadow-medium hover:shadow-glow transition-all duration-500 border-0 bg-gradient-card backdrop-blur-sm hover:scale-105 animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CardHeader className="text-center pb-4">
                  <div
                    className={`w-20 h-20 ${feature.bgColor} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <feature.icon
                      className={`h-10 w-10 ${feature.color} group-hover:rotate-12 transition-transform duration-300`}
                    />
                  </div>
                  <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center text-base leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Issue Types Overview */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-foreground mb-4">
              Current Issue Categories
            </h3>
            <p className="text-xl text-muted-foreground">
              Overview of reported issues by category
            </p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
            {issueTypes.map((type, index) => (
              <Card
                key={index}
                className="text-center shadow-soft hover:shadow-medium transition-all duration-200 cursor-pointer"
              >
                <CardContent className="pt-6">
                  <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <type.icon className="h-6 w-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-foreground mb-2">
                    {type.label}
                  </h4>
                  <Badge variant="secondary" className="text-lg font-bold">
                    {type.count} Issues
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <h3 className="text-3xl font-bold mb-4">
            Ready to Make a Difference?
          </h3>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join thousands of citizens and government officials working together
            to build better communities.
          </p>
          <Button
            variant="hero"
            size="lg"
            className="bg-white text-civic-green hover:bg-white/90"
            onClick={() => navigate("/report")}
          >
            Get Started Today
          </Button>
        </div>
      </section>
    </div>
  );
};

export default Index;

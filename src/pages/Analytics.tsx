import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  ArrowLeft,
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  MapPin,
  Clock,
  Users,
  CheckCircle,
  AlertTriangle,
  Shield,
  Download,
  Filter
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Profile {
  role: string;
  full_name: string;
}

const Analytics = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState("30d");

  // Mock analytics data
  const analyticsData = {
    overview: {
      totalIssues: 1247,
      resolvedIssues: 1089,
      avgResolutionTime: 2.4,
      citizenSatisfaction: 87,
      trendsUp: [
        { metric: "Resolution Rate", value: "+12%", period: "vs last month" },
        { metric: "Response Time", value: "-18%", period: "vs last month" },
        { metric: "Citizen Reports", value: "+24%", period: "vs last month" }
      ]
    },
    categories: [
      { name: "Road Issues", count: 342, resolved: 298, percentage: 87 },
      { name: "Street Lighting", count: 189, resolved: 176, percentage: 93 },
      { name: "Sanitation", count: 267, resolved: 234, percentage: 88 },
      { name: "Water Supply", count: 156, resolved: 142, percentage: 91 },
      { name: "Electrical", count: 134, resolved: 118, percentage: 88 },
      { name: "Infrastructure", count: 159, resolved: 121, percentage: 76 }
    ],
    departments: [
      { name: "Public Works Department", issues: 501, avgTime: 3.2, satisfaction: 85 },
      { name: "Electrical Services", issues: 323, avgTime: 1.8, satisfaction: 92 },
      { name: "Water & Sanitation", issues: 423, avgTime: 2.1, satisfaction: 89 },
      { name: "Municipal Corporation", issues: 267, avgTime: 4.1, satisfaction: 78 }
    ],
    locations: [
      { area: "Ranchi Central", issues: 234, resolved: 198 },
      { area: "Dhanbad", issues: 189, resolved: 167 },
      { area: "Bokaro", issues: 156, resolved: 142 },
      { area: "Jamshedpur", issues: 198, resolved: 178 },
      { area: "Hazaribagh", issues: 123, resolved: 108 }
    ]
  };

  useEffect(() => {
    checkUserRole();
  }, []);

  const checkUserRole = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        navigate('/auth');
        return;
      }

      const { data: profileData, error } = await supabase
        .from('profiles')
        .select('role, full_name')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        toast({
          title: "Access Error",
          description: "Unable to verify admin access. Please try again.",
          variant: "destructive"
        });
        navigate('/');
        return;
      }

      if (profileData?.role !== 'admin' && profileData?.role !== 'moderator') {
        toast({
          title: "Access Denied",
          description: "You don't have permission to access analytics.",
          variant: "destructive"
        });
        navigate('/');
        return;
      }

      setProfile(profileData);
    } catch (error) {
      console.error('Error checking user role:', error);
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return "bg-green-500";
    if (percentage >= 80) return "bg-blue-500";
    if (percentage >= 70) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getSatisfactionColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 80) return "text-blue-600";
    if (score >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading analytics...</p>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Alert variant="destructive" className="max-w-md">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Access denied. Admin privileges required.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-card shadow-soft border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" onClick={() => navigate('/admin')}>
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-foreground flex items-center gap-2">
                  <BarChart3 className="h-6 w-6 text-primary" />
                  Analytics Dashboard
                </h1>
                <p className="text-muted-foreground">
                  Comprehensive insights and performance metrics
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 3 months</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="shadow-medium">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Issues</p>
                  <p className="text-3xl font-bold text-foreground">{analyticsData.overview.totalIssues.toLocaleString()}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-medium">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Resolution Rate</p>
                  <p className="text-3xl font-bold text-green-600">
                    {Math.round((analyticsData.overview.resolvedIssues / analyticsData.overview.totalIssues) * 100)}%
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-medium">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg Resolution Time</p>
                  <p className="text-3xl font-bold text-blue-600">{analyticsData.overview.avgResolutionTime} days</p>
                </div>
                <Clock className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-medium">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Satisfaction Score</p>
                  <p className="text-3xl font-bold text-purple-600">{analyticsData.overview.citizenSatisfaction}%</p>
                </div>
                <Users className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Trends */}
        <Card className="shadow-medium">
          <CardHeader>
            <CardTitle>Performance Trends</CardTitle>
            <CardDescription>Key performance indicators compared to previous period</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {analyticsData.overview.trendsUp.map((trend, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium">{trend.metric}</p>
                    <p className="text-sm text-muted-foreground">
                      <span className="text-green-600 font-medium">{trend.value}</span> {trend.period}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Category Performance */}
          <Card className="shadow-medium">
            <CardHeader>
              <CardTitle>Issues by Category</CardTitle>
              <CardDescription>Resolution rates across different issue types</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.categories.map((category, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">{category.name}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-muted-foreground">
                          {category.resolved}/{category.count}
                        </span>
                        <Badge variant="outline">{category.percentage}%</Badge>
                      </div>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${getProgressColor(category.percentage)}`}
                        style={{ width: `${category.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Department Performance */}
          <Card className="shadow-medium">
            <CardHeader>
              <CardTitle>Department Performance</CardTitle>
              <CardDescription>Efficiency metrics by government department</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.departments.map((dept, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium">{dept.name}</h4>
                      <Badge variant="outline">{dept.issues} issues</Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Avg Resolution Time</p>
                        <p className="font-medium">{dept.avgTime} days</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Satisfaction</p>
                        <p className={`font-medium ${getSatisfactionColor(dept.satisfaction)}`}>
                          {dept.satisfaction}%
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Geographic Distribution */}
        <Card className="shadow-medium">
          <CardHeader>
            <CardTitle>Geographic Distribution</CardTitle>
            <CardDescription>Issue distribution across different areas in Jharkhand</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {analyticsData.locations.map((location, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-primary" />
                      <h4 className="font-medium">{location.area}</h4>
                    </div>
                    <Badge variant="outline">{location.issues} issues</Badge>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Resolved</span>
                      <span className="font-medium">{location.resolved}/{location.issues}</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full"
                        style={{ width: `${(location.resolved / location.issues) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Export Options */}
        <Card className="shadow-medium">
          <CardHeader>
            <CardTitle>Export & Reports</CardTitle>
            <CardDescription>Generate detailed reports for stakeholders</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="justify-start">
                <Download className="h-4 w-4 mr-2" />
                Monthly Report (PDF)
              </Button>
              <Button variant="outline" className="justify-start">
                <Download className="h-4 w-4 mr-2" />
                Performance Data (CSV)
              </Button>
              <Button variant="outline" className="justify-start">
                <Download className="h-4 w-4 mr-2" />
                Executive Summary
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Analytics;

// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://bnixuzjvjvesvztobexh.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJuaXh1emp2anZlc3Z6dG9iZXhoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg1MjIzMDEsImV4cCI6MjA3NDA5ODMwMX0.20Zfa1U0ysGK6FHyWwKv--mnQ_VQ9ZdzMXVNEEAn0O8";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});
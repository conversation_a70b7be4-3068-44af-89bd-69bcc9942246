@tailwind base;
@tailwind components;
@tailwind utilities;

@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here.
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 215 25% 27%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    --primary: 142 73% 32%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 215 25% 27%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 200 90% 50%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --success: 142 73% 32%;
    --success-foreground: 0 0% 100%;

    --warning: 43 96% 56%;
    --warning-foreground: 215 25% 27%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142 73% 32%;

    /* Civic theme colors */
    --civic-blue: 200 90% 50%;
    --civic-green: 142 73% 32%;
    --civic-green-light: 142 60% 45%;
    --civic-gray: 215 25% 27%;
    --civic-gray-light: 210 20% 85%;

    /* Enhanced Gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(var(--civic-green)),
      hsl(var(--civic-green-light))
    );
    --gradient-hero: linear-gradient(
      135deg,
      hsl(var(--civic-blue)) 0%,
      hsl(var(--civic-green)) 50%,
      hsl(var(--warning)) 100%
    );
    --gradient-card: linear-gradient(
      145deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    --gradient-glass: linear-gradient(
      145deg,
      rgba(255, 255, 255, 0.25) 0%,
      rgba(255, 255, 255, 0.1) 100%
    );
    --gradient-button: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--civic-green-light)) 100%
    );
    --gradient-accent: linear-gradient(
      135deg,
      hsl(var(--accent)) 0%,
      hsl(var(--warning)) 100%
    );

    /* Enhanced Shadows with depth */
    --shadow-soft: 0 2px 8px -2px hsl(var(--civic-gray) / 0.08),
      0 1px 4px -1px hsl(var(--civic-gray) / 0.06);
    --shadow-medium: 0 4px 16px -4px hsl(var(--civic-gray) / 0.12),
      0 2px 8px -2px hsl(var(--civic-gray) / 0.08);
    --shadow-strong: 0 8px 32px -8px hsl(var(--civic-gray) / 0.16),
      0 4px 16px -4px hsl(var(--civic-gray) / 0.12);
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.15);
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-backdrop: blur(20px);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 215 28% 17%;
    --foreground: 210 20% 98%;

    --card: 215 28% 19%;
    --card-foreground: 210 20% 98%;

    --popover: 215 28% 19%;
    --popover-foreground: 210 20% 98%;

    --primary: 142 73% 45%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 20% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 200 90% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 50%;
    --destructive-foreground: 0 0% 100%;

    --success: 142 73% 45%;
    --success-foreground: 0 0% 100%;

    --warning: 43 96% 60%;
    --warning-foreground: 215 25% 27%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142 73% 45%;

    /* Dark mode civic colors */
    --civic-blue: 200 90% 60%;
    --civic-green: 142 73% 45%;
    --civic-green-light: 142 60% 55%;
    --civic-gray: 210 20% 98%;
    --civic-gray-light: 217.2 32.6% 25%;

    /* Dark mode enhanced gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(var(--civic-green)),
      hsl(var(--civic-green-light))
    );
    --gradient-hero: linear-gradient(
      135deg,
      hsl(var(--civic-blue)) 0%,
      hsl(var(--civic-green)) 50%,
      hsl(var(--warning)) 100%
    );
    --gradient-card: linear-gradient(
      145deg,
      rgba(51, 65, 85, 0.9) 0%,
      rgba(51, 65, 85, 0.7) 100%
    );
    --gradient-glass: linear-gradient(
      145deg,
      rgba(51, 65, 85, 0.25) 0%,
      rgba(51, 65, 85, 0.1) 100%
    );
    --gradient-button: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--civic-green-light)) 100%
    );
    --gradient-accent: linear-gradient(
      135deg,
      hsl(var(--accent)) 0%,
      hsl(var(--warning)) 100%
    );

    /* Dark mode enhanced shadows */
    --shadow-soft: 0 2px 8px -2px hsl(0 0% 0% / 0.25),
      0 1px 4px -1px hsl(0 0% 0% / 0.15);
    --shadow-medium: 0 4px 16px -4px hsl(0 0% 0% / 0.35),
      0 2px 8px -2px hsl(0 0% 0% / 0.25);
    --shadow-strong: 0 8px 32px -8px hsl(0 0% 0% / 0.45),
      0 4px 16px -4px hsl(0 0% 0% / 0.35);
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.25);
    --shadow-glass: 0 8px 32px 0 rgba(0, 0, 0, 0.5);

    /* Dark glassmorphism */
    --glass-bg: rgba(51, 65, 85, 0.25);
    --glass-border: rgba(148, 163, 184, 0.18);
    --glass-backdrop: blur(20px);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
      "Roboto", sans-serif;
    font-feature-settings: "cv11", "ss01";
    font-variation-settings: "opsz" 32;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2 ring-offset-background;
  }
}

@layer components {
  /* Glass morphism card */
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-glass);
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-hero {
    background: var(--gradient-hero);
  }

  .bg-gradient-card {
    background: var(--gradient-card);
  }

  .bg-gradient-glass {
    background: var(--gradient-glass);
  }

  /* Enhanced shadows */
  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }

  .shadow-strong {
    box-shadow: var(--shadow-strong);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  /* Floating animation */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  /* Pulse glow animation */
  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  @keyframes pulse-glow {
    from {
      box-shadow: 0 0 20px hsl(var(--primary) / 0.1);
    }
    to {
      box-shadow: 0 0 30px hsl(var(--primary) / 0.3);
    }
  }

  /* Slide up animation */
  .animate-slide-up {
    animation: slide-up 0.5s ease-out;
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Scale in animation */
  .animate-scale-in {
    animation: scale-in 0.3s ease-out;
  }

  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}

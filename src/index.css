@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 210 20% 98%;
    --foreground: 215 25% 27%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    --primary: 142 73% 32%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 215 25% 27%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 200 90% 50%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --success: 142 73% 32%;
    --success-foreground: 0 0% 100%;

    --warning: 43 96% 56%;
    --warning-foreground: 215 25% 27%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142 73% 32%;

    /* Civic theme colors */
    --civic-blue: 200 90% 50%;
    --civic-green: 142 73% 32%;
    --civic-green-light: 142 60% 45%;
    --civic-gray: 215 25% 27%;
    --civic-gray-light: 210 20% 85%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--civic-green)), hsl(var(--civic-green-light)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--civic-blue)), hsl(var(--civic-green)));
    --gradient-card: linear-gradient(180deg, hsl(var(--background)), hsl(var(--card)));

    /* Shadows */
    --shadow-soft: 0 2px 8px -2px hsl(var(--civic-gray) / 0.1);
    --shadow-medium: 0 4px 16px -4px hsl(var(--civic-gray) / 0.15);
    --shadow-strong: 0 8px 32px -8px hsl(var(--civic-gray) / 0.2);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 215 28% 17%;
    --foreground: 210 20% 98%;

    --card: 215 28% 19%;
    --card-foreground: 210 20% 98%;

    --popover: 215 28% 19%;
    --popover-foreground: 210 20% 98%;

    --primary: 142 73% 45%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 20% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 200 90% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 50%;
    --destructive-foreground: 0 0% 100%;

    --success: 142 73% 45%;
    --success-foreground: 0 0% 100%;

    --warning: 43 96% 60%;
    --warning-foreground: 215 25% 27%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142 73% 45%;

    /* Dark mode civic colors */
    --civic-blue: 200 90% 60%;
    --civic-green: 142 73% 45%;
    --civic-green-light: 142 60% 55%;
    --civic-gray: 210 20% 98%;
    --civic-gray-light: 217.2 32.6% 25%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--civic-green)), hsl(var(--civic-green-light)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--civic-blue)), hsl(var(--civic-green)));
    --gradient-card: linear-gradient(180deg, hsl(var(--background)), hsl(var(--card)));

    /* Dark mode shadows */
    --shadow-soft: 0 2px 8px -2px hsl(0 0% 0% / 0.3);
    --shadow-medium: 0 4px 16px -4px hsl(0 0% 0% / 0.4);
    --shadow-strong: 0 8px 32px -8px hsl(0 0% 0% / 0.5);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

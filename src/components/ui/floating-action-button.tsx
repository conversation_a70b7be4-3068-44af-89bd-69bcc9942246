import * as React from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";

interface FloatingActionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  icon?: React.ReactNode;
  expanded?: boolean;
  actions?: Array<{
    icon: React.ReactNode;
    label: string;
    onClick: () => void;
    className?: string;
  }>;
}

const FloatingActionButton = React.forwardRef<HTMLButtonElement, FloatingActionButtonProps>(
  ({ className, icon = <Plus className="h-6 w-6" />, expanded = false, actions = [], ...props }, ref) => {
    const [isExpanded, setIsExpanded] = React.useState(expanded);

    const toggleExpanded = () => {
      if (actions.length > 0) {
        setIsExpanded(!isExpanded);
      }
    };

    return (
      <div className="fixed bottom-6 right-6 z-50">
        {/* Action items */}
        {actions.length > 0 && isExpanded && (
          <div className="flex flex-col gap-3 mb-4">
            {actions.map((action, index) => (
              <div
                key={index}
                className="flex items-center gap-3 animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <span className="bg-black/80 text-white px-3 py-1 rounded-lg text-sm font-medium whitespace-nowrap">
                  {action.label}
                </span>
                <Button
                  size="sm"
                  className={cn(
                    "h-12 w-12 rounded-full shadow-glow hover:shadow-strong transition-all duration-300 hover:scale-110",
                    "bg-white text-gray-900 hover:bg-white/90",
                    action.className
                  )}
                  onClick={action.onClick}
                >
                  {action.icon}
                </Button>
              </div>
            ))}
          </div>
        )}

        {/* Main FAB */}
        <Button
          ref={ref}
          className={cn(
            "h-16 w-16 rounded-full shadow-glow hover:shadow-strong transition-all duration-300 hover:scale-110",
            "bg-gradient-primary text-white border-0",
            "animate-pulse-glow",
            className
          )}
          onClick={actions.length > 0 ? toggleExpanded : props.onClick}
          {...props}
        >
          <div className={cn("transition-transform duration-300", isExpanded && "rotate-45")}>
            {isExpanded && actions.length > 0 ? <X className="h-6 w-6" /> : icon}
          </div>
        </Button>
      </div>
    );
  }
);

FloatingActionButton.displayName = "FloatingActionButton";

export { FloatingActionButton };

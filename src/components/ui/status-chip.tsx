import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { CheckCircle, Clock, AlertTriangle, XCircle, Zap } from "lucide-react";

const statusChipVariants = cva(
  "inline-flex items-center gap-2 rounded-full px-3 py-1 text-xs font-medium transition-all duration-200 hover:scale-105",
  {
    variants: {
      variant: {
        pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
        "in-progress": "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
        completed: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
        cancelled: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300",
        urgent: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300 animate-pulse",
      },
      size: {
        sm: "px-2 py-0.5 text-xs",
        md: "px-3 py-1 text-xs",
        lg: "px-4 py-2 text-sm",
      },
    },
    defaultVariants: {
      variant: "pending",
      size: "md",
    },
  }
);

const statusIcons = {
  pending: Clock,
  "in-progress": Zap,
  completed: CheckCircle,
  cancelled: XCircle,
  urgent: AlertTriangle,
};

export interface StatusChipProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statusChipVariants> {
  status: keyof typeof statusIcons;
  showIcon?: boolean;
  pulse?: boolean;
}

const StatusChip = React.forwardRef<HTMLDivElement, StatusChipProps>(
  ({ className, variant, size, status, showIcon = true, pulse = false, children, ...props }, ref) => {
    const Icon = statusIcons[status];
    
    return (
      <div
        className={cn(
          statusChipVariants({ variant: variant || status, size }),
          pulse && "animate-pulse",
          className
        )}
        ref={ref}
        {...props}
      >
        {showIcon && Icon && <Icon className="h-3 w-3" />}
        {children || status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
      </div>
    );
  }
);

StatusChip.displayName = "StatusChip";

export { StatusChip, statusChipVariants };

import * as React from "react";
import { cn } from "@/lib/utils";

interface ProgressBarProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number;
  max?: number;
  showLabel?: boolean;
  label?: string;
  variant?: "default" | "success" | "warning" | "danger";
  size?: "sm" | "md" | "lg";
  animated?: boolean;
  striped?: boolean;
}

const ProgressBar = React.forwardRef<HTMLDivElement, ProgressBarProps>(
  ({
    className,
    value,
    max = 100,
    showLabel = false,
    label,
    variant = "default",
    size = "md",
    animated = false,
    striped = false,
    ...props
  }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

    const variantClasses = {
      default: "bg-primary",
      success: "bg-green-500",
      warning: "bg-yellow-500",
      danger: "bg-red-500",
    };

    const sizeClasses = {
      sm: "h-2",
      md: "h-3",
      lg: "h-4",
    };

    const backgroundClasses = {
      default: "bg-primary/20",
      success: "bg-green-500/20",
      warning: "bg-yellow-500/20",
      danger: "bg-red-500/20",
    };

    return (
      <div className={cn("w-full", className)} ref={ref} {...props}>
        {showLabel && (
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-foreground">
              {label || `${Math.round(percentage)}%`}
            </span>
            <span className="text-sm text-muted-foreground">
              {value}/{max}
            </span>
          </div>
        )}
        
        <div
          className={cn(
            "w-full rounded-full overflow-hidden",
            sizeClasses[size],
            backgroundClasses[variant]
          )}
        >
          <div
            className={cn(
              "h-full rounded-full transition-all duration-500 ease-out",
              variantClasses[variant],
              animated && "animate-pulse",
              striped && "bg-gradient-to-r from-transparent via-white/20 to-transparent bg-[length:20px_20px] animate-shimmer"
            )}
            style={{ width: `${percentage}%` }}
          />
        </div>
        
        {!showLabel && (
          <div className="mt-1 text-right">
            <span className="text-xs text-muted-foreground">
              {Math.round(percentage)}%
            </span>
          </div>
        )}
      </div>
    );
  }
);

ProgressBar.displayName = "ProgressBar";

export { ProgressBar };

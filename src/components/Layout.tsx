import { ReactNode, useEffect, useState } from "react";
import { User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  MapPin, 
  Menu, 
  X, 
  User as UserIcon, 
  LogOut, 
  Bell,
  Home,
  FileText,
  BarChart3,
  Settings,
  Phone,
  HelpCircle,
  Shield
} from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";

interface LayoutProps {
  children: ReactNode;
}

interface Profile {
  role: string;
  full_name: string;
}

const Layout = ({ children }: LayoutProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      if (session?.user) {
        fetchProfile(session.user.id);
      }
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null);
        if (session?.user) {
          fetchProfile(session.user.id);
        } else {
          setProfile(null);
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('role, full_name')
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      setProfile(data);
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      toast({
        title: "Signed out successfully",
        description: "You have been signed out of your account.",
      });
      navigate("/");
    } catch (error) {
      toast({
        title: "Error signing out",
        description: "Please try again.",
        variant: "destructive",
      });
    }
  };

  const navigation = [
    { name: "Home", href: "/", icon: Home },
    { name: "Report Issue", href: "/report", icon: FileText },
    { name: "My Reports", href: "/my-reports", icon: UserIcon },
    ...(profile?.role === "admin" || profile?.role === "moderator" 
      ? [
          { name: "Admin Dashboard", href: "/admin", icon: BarChart3 },
          { name: "Analytics", href: "/analytics", icon: BarChart3 },
        ] 
      : []
    ),
    { name: "About", href: "/about", icon: HelpCircle },
    { name: "Contact", href: "/contact", icon: Phone },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-primary rounded-xl flex items-center justify-center mx-auto mb-4 animate-pulse">
            <MapPin className="h-8 w-8 text-white" />
          </div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-card shadow-soft border-b sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3 cursor-pointer" onClick={() => navigate("/")}>
              <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                <MapPin className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-foreground">CivicReport</h1>
                <p className="text-xs text-muted-foreground">Government of Jharkhand</p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              {navigation.map((item) => (
                <Button
                  key={item.name}
                  variant={location.pathname === item.href ? "default" : "ghost"}
                  onClick={() => navigate(item.href)}
                  className="flex items-center space-x-2"
                >
                  <item.icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Button>
              ))}
            </nav>

            {/* User Menu */}
            <div className="flex items-center space-x-2">
              {user ? (
                <>
                  <Button variant="ghost" size="sm">
                    <Bell className="h-4 w-4" />
                  </Button>
                  <div className="flex items-center space-x-2">
                    <div className="hidden md:block text-right">
                      <p className="text-sm font-medium text-foreground">
                        {profile?.full_name || user.email}
                      </p>
                      {profile?.role && (
                        <Badge variant="secondary" className="text-xs">
                          {profile.role === "admin" && <Shield className="h-3 w-3 mr-1" />}
                          {profile.role}
                        </Badge>
                      )}
                    </div>
                    <Button variant="ghost" size="sm" onClick={handleSignOut}>
                      <LogOut className="h-4 w-4" />
                    </Button>
                  </div>
                </>
              ) : (
                <Button onClick={() => navigate("/auth")} className="bg-gradient-primary hover:opacity-90">
                  Sign In
                </Button>
              )}

              {/* Mobile menu button */}
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMobileMenuOpen && (
            <div className="lg:hidden py-4 border-t">
              <nav className="flex flex-col space-y-2">
                {navigation.map((item) => (
                  <Button
                    key={item.name}
                    variant={location.pathname === item.href ? "default" : "ghost"}
                    onClick={() => {
                      navigate(item.href);
                      setIsMobileMenuOpen(false);
                    }}
                    className="justify-start"
                  >
                    <item.icon className="h-4 w-4 mr-2" />
                    {item.name}
                  </Button>
                ))}
              </nav>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-civic-gray text-white py-12 mt-20">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                  <MapPin className="h-5 w-5 text-white" />
                </div>
                <span className="text-lg font-semibold">CivicReport</span>
              </div>
              <p className="text-white/80 text-sm">
                Empowering communities through technology and civic engagement.
              </p>
            </div>

            <div>
              <h5 className="font-semibold mb-4">Quick Links</h5>
              <ul className="space-y-2 text-sm text-white/80">
                <li><button onClick={() => navigate("/about")} className="hover:text-white">About Us</button></li>
                <li><button onClick={() => navigate("/contact")} className="hover:text-white">Contact</button></li>
                <li><button onClick={() => navigate("/privacy")} className="hover:text-white">Privacy Policy</button></li>
                <li><button onClick={() => navigate("/help")} className="hover:text-white">Help & Support</button></li>
              </ul>
            </div>

            <div>
              <h5 className="font-semibold mb-4">Government Links</h5>
              <ul className="space-y-2 text-sm text-white/80">
                <li>Department of Higher Education</li>
                <li>Government of Jharkhand</li>
                <li>Clean & Green Technology</li>
                <li>Municipal Corporation</li>
              </ul>
            </div>

            <div>
              <h5 className="font-semibold mb-4">Contact Info</h5>
              <div className="space-y-2 text-sm text-white/80">
                <p>Email: <EMAIL></p>
                <p>Phone: +91-XXX-XXX-XXXX</p>
                <p>Office Hours: Mon-Fri 9AM-6PM</p>
              </div>
            </div>
          </div>

          <div className="border-t border-white/20 mt-8 pt-8 text-center text-white/60 text-sm">
            <p>&copy; 2024 Government of Jharkhand. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;